# Bitbucket Pull Request Reviewer

A comprehensive Python tool for automatically analyzing and reviewing pull requests using the Bitbucket Cloud REST API v2.0.

## Features

- **Automated Code Analysis**: Analyzes code changes for quality issues, security concerns, and best practices
- **Language-Specific Reviews**: Supports TypeScript/JavaScript, Python, and Java with language-specific checks
- **Line-by-Line Comments**: Creates inline comments on specific lines where issues are found
- **Comprehensive Scoring**: Provides a 1-10 score based on code quality and issues found
- **Automatic Approval/Rejection**: Can automatically approve or reject pull requests based on analysis
- **Detailed Reporting**: Generates comprehensive review summaries with recommendations

## Supported Code Quality Checks

### General Checks
- Large file changes (>100 lines)
- Hardcoded credentials detection
- TODO comments tracking
- Security vulnerability patterns

### TypeScript/JavaScript
- `console.log` statements
- Hardcoded URLs
- TODO comments
- Code structure analysis

### Python
- `print()` statements (recommends logging)
- Bare `except:` clauses
- Code quality patterns

### Java
- `System.out.println` statements
- Logging framework recommendations

## Installation

1. Clone this repository
2. Install required dependencies:
```bash
pip install requests
```

## Configuration

### Bitbucket API Key
You need a Bitbucket API key with the following scopes:
- `pullrequest` - for reading pull requests
- `pullrequest:write` - for creating comments and approvals
- `repository` - for accessing repository content

### Getting an API Key
1. Go to Bitbucket Settings → App passwords
2. Create a new app password with the required scopes
3. Use the generated token in your configuration

## Usage

### Basic Usage

```python
from bitbucket_pr_reviewer import BitbucketPRReviewer

# Initialize with your API key
reviewer = BitbucketPRReviewer("your-api-key-here")

# Review a pull request
result = reviewer.submit_review("https://bitbucket.org/workspace/repo/pull-requests/123")

print(f"Review Status: {result['status']}")
print(f"Score: {result['score']}/10")
```

### Advanced Usage

```python
# Parse PR URL components
workspace, repo_slug, pr_id = reviewer.parse_pr_url(pr_url)

# Get PR details
pr_data = reviewer.get_pull_request(workspace, repo_slug, pr_id)

# Get diff content
diff_content = reviewer.get_pull_request_diff(workspace, repo_slug, pr_id)

# Analyze changes
review_result = reviewer.analyze_code_changes(diff_content, pr_data)

# Create individual comments
for comment in review_result.comments:
    reviewer.create_comment(workspace, repo_slug, pr_id, comment)

# Approve if score is high enough
if review_result.status == 'approved':
    reviewer.approve_pull_request(workspace, repo_slug, pr_id)
```

## API Endpoints Used

The tool uses the following Bitbucket Cloud REST API v2.0 endpoints:

- `GET /2.0/repositories/{workspace}/{repo_slug}/pullrequests/{pr_id}` - Get PR details
- `GET /2.0/repositories/{workspace}/{repo_slug}/diff/{spec}` - Get PR diff
- `POST /2.0/repositories/{workspace}/{repo_slug}/pullrequests/{pr_id}/comments` - Create comments
- `POST /2.0/repositories/{workspace}/{repo_slug}/pullrequests/{pr_id}/approve` - Approve PR

## Review Scoring System

The tool uses a 1-10 scoring system:

- **8-10**: Excellent code quality, minimal issues → **APPROVED**
- **6-7**: Good code with minor issues → **NEEDS WORK**
- **1-5**: Significant issues found → **REJECTED**

### Score Deductions
- Critical issues (hardcoded credentials): -3 points each
- Major issues (large file changes): -2 points each
- Minor issues (style, best practices): -0.5 points each

### Score Bonuses
- Small, focused changes (≤3 files): +1 point

## Comment Types

### Inline Comments
Created on specific lines where issues are detected:
```python
ReviewComment(
    content="Consider removing console.log statements before merging to production.",
    file_path="src/app.ts",
    line_from=42,
    is_inline=True
)
```

### General Comments
Overall review summary and recommendations:
```python
ReviewComment(
    content="## Review Summary\n\nThis PR demonstrates good code quality..."
)
```

## Error Handling

The tool includes comprehensive error handling for:
- Invalid PR URLs
- API authentication failures
- Network connectivity issues
- Malformed API responses
- Permission errors

## Example Output

```
Starting pull request review...
Reviewing PR 2533 in technofarm/geoscan-angular-8
Fetching pull request details...
Analyzing code changes...
Creating review summary...
Creating 3 detailed comments...

==================================================
REVIEW COMPLETED
==================================================
Status: approved
Score: 8/10
Comments Created: 4
==================================================
```

## Limitations

- Currently supports only Bitbucket Cloud (not Server/Data Center)
- Requires appropriate API permissions
- Analysis is based on diff content only (no full file context)
- Language detection is based on file extensions

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## License

This project is licensed under the MIT License.

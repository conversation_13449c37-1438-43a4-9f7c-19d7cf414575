#!/usr/bin/env python3
"""
Test Bitbucket API Connection
Validates API key and tests basic connectivity
"""

import requests
import json
from bitbucket_pr_reviewer import Bitbu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def test_api_key(api_key: str):
    """Test if the API key is valid by making a simple API call"""
    
    print("="*50)
    print("BITBUCKET API CONNECTION TEST")
    print("="*50)
    
    # Test basic authentication with user endpoint
    session = requests.Session()
    session.headers.update({
        'Authorization': f'Bearer {api_key}',
        'Accept': 'application/json'
    })
    
    print(f"\n1. Testing API Key Format")
    print("-" * 30)
    print(f"API Key Length: {len(api_key)} characters")
    print(f"API Key Prefix: {api_key[:10]}...")
    print(f"API Key Suffix: ...{api_key[-10:]}")
    
    print(f"\n2. Testing Basic Authentication")
    print("-" * 30)
    
    try:
        # Test with user endpoint (should work with any valid token)
        response = session.get("https://api.bitbucket.org/2.0/user")
        
        if response.status_code == 200:
            user_data = response.json()
            print("✅ API Key is VALID")
            print(f"   Authenticated as: {user_data.get('display_name', 'Unknown')}")
            print(f"   Username: {user_data.get('username', 'Unknown')}")
            print(f"   Account ID: {user_data.get('account_id', 'Unknown')}")
            return True
        else:
            print("❌ API Key is INVALID")
            print(f"   Status Code: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print("❌ Connection Error")
        print(f"   Error: {e}")
        return False


def test_repository_access(api_key: str, workspace: str, repo_slug: str):
    """Test access to a specific repository"""
    
    print(f"\n3. Testing Repository Access")
    print("-" * 30)
    
    session = requests.Session()
    session.headers.update({
        'Authorization': f'Bearer {api_key}',
        'Accept': 'application/json'
    })
    
    try:
        url = f"https://api.bitbucket.org/2.0/repositories/{workspace}/{repo_slug}"
        response = session.get(url)
        
        if response.status_code == 200:
            repo_data = response.json()
            print(f"✅ Repository Access: GRANTED")
            print(f"   Repository: {repo_data.get('full_name', 'Unknown')}")
            print(f"   Language: {repo_data.get('language', 'Unknown')}")
            print(f"   Private: {repo_data.get('is_private', 'Unknown')}")
            return True
        elif response.status_code == 404:
            print(f"❌ Repository Access: NOT FOUND")
            print(f"   Repository {workspace}/{repo_slug} does not exist or you don't have access")
            return False
        elif response.status_code == 403:
            print(f"❌ Repository Access: FORBIDDEN")
            print(f"   You don't have permission to access {workspace}/{repo_slug}")
            return False
        else:
            print(f"❌ Repository Access: ERROR")
            print(f"   Status Code: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection Error: {e}")
        return False


def test_pull_request_access(api_key: str, workspace: str, repo_slug: str, pr_id: int):
    """Test access to a specific pull request"""
    
    print(f"\n4. Testing Pull Request Access")
    print("-" * 30)
    
    session = requests.Session()
    session.headers.update({
        'Authorization': f'Bearer {api_key}',
        'Accept': 'application/json'
    })
    
    try:
        url = f"https://api.bitbucket.org/2.0/repositories/{workspace}/{repo_slug}/pullrequests/{pr_id}"
        response = session.get(url)
        
        if response.status_code == 200:
            pr_data = response.json()
            print(f"✅ Pull Request Access: GRANTED")
            print(f"   PR #{pr_data.get('id', 'Unknown')}: {pr_data.get('title', 'Unknown')}")
            print(f"   Author: {pr_data.get('author', {}).get('display_name', 'Unknown')}")
            print(f"   State: {pr_data.get('state', 'Unknown')}")
            return True
        elif response.status_code == 404:
            print(f"❌ Pull Request Access: NOT FOUND")
            print(f"   PR #{pr_id} does not exist or you don't have access")
            return False
        elif response.status_code == 403:
            print(f"❌ Pull Request Access: FORBIDDEN")
            print(f"   You don't have permission to access PR #{pr_id}")
            return False
        else:
            print(f"❌ Pull Request Access: ERROR")
            print(f"   Status Code: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection Error: {e}")
        return False


def provide_troubleshooting_guide():
    """Provide troubleshooting guidance"""
    
    print(f"\n5. Troubleshooting Guide")
    print("-" * 30)
    print("""
Common Issues and Solutions:

🔑 API Key Issues:
   • Ensure you're using an App Password, not your account password
   • Create at: https://bitbucket.org/account/settings/app-passwords/
   • Required scopes: pullrequest, pullrequest:write, repository

🏢 Repository Access:
   • Verify you have access to the repository
   • Check if the repository is private and you're a member
   • Ensure workspace and repository names are correct

📝 Pull Request Access:
   • Verify the PR number exists
   • Check if the PR has been merged or deleted
   • Ensure you have permission to view the PR

🌐 Network Issues:
   • Check your internet connection
   • Verify firewall settings allow HTTPS to api.bitbucket.org
   • Try again in a few minutes (rate limiting)

📋 Permission Scopes:
   • pullrequest: Read pull requests
   • pullrequest:write: Create comments and approve/reject
   • repository: Access repository content and diffs
    """)


def main():
    """Main test function"""
    
    # Configuration
    API_KEY = "ATATT3xFfGF0TN056ovkNC5hzIwdB8q7PMGK5Zny39O-8qY0p5MMuH7TpqIkaZN9eGPWahGgMKmpdIbGarks30dBeiQS7Ymfqk515jPV4XWaYBfz5oGbz2lbM-r4na8LKjeNFCn_9MCzRBNKd8lKbt7peYGtR-l07kreV5O2wqcytMnfM-SgwT4=2902B8ED"
    WORKSPACE = "technofarm"
    REPO_SLUG = "geoscan-angular-8"
    PR_ID = 2533
    
    # Run tests
    api_valid = test_api_key(API_KEY)
    
    if api_valid:
        repo_access = test_repository_access(API_KEY, WORKSPACE, REPO_SLUG)
        
        if repo_access:
            pr_access = test_pull_request_access(API_KEY, WORKSPACE, REPO_SLUG, PR_ID)
    
    # Always show troubleshooting guide
    provide_troubleshooting_guide()
    
    print(f"\n" + "="*50)
    print("CONNECTION TEST COMPLETED")
    print("="*50)


if __name__ == "__main__":
    main()

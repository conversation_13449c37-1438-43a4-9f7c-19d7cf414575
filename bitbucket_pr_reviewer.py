#!/usr/bin/env python3
"""
Bitbucket Pull Request Reviewer
Comprehensive tool for analyzing and reviewing pull requests using Bitbucket Cloud REST API v2.0
"""

import requests
import json
import re
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from urllib.parse import urlparse, parse_qs
import base64


@dataclass
class ReviewComment:
    """Represents a review comment with optional line-specific targeting"""
    content: str
    file_path: Optional[str] = None
    line_from: Optional[int] = None
    line_to: Optional[int] = None
    is_inline: bool = False


@dataclass
class ReviewResult:
    """Represents the overall review result"""
    status: str  # 'approved', 'rejected', 'needs_work'
    summary: str
    comments: List[ReviewComment]
    score: int  # 1-10 rating


class BitbucketPRReviewer:
    """
    Bitbucket Pull Request Reviewer using REST API v2.0
    
    Supports:
    - Fetching PR details and diff
    - Analyzing code changes
    - Creating line-by-line comments
    - Submitting approval/rejection with reasoning
    """
    
    def __init__(self, api_key: str):
        """
        Initialize the reviewer with Bitbucket API credentials
        
        Args:
            api_key: Bitbucket API key for authentication
        """
        self.api_key = api_key
        self.base_url = "https://api.bitbucket.org/2.0"
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {api_key}',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        })
    
    def parse_pr_url(self, pr_url: str) -> Tuple[str, str, int]:
        """
        Parse Bitbucket PR URL to extract workspace, repo_slug, and PR ID
        
        Args:
            pr_url: Full Bitbucket PR URL
            
        Returns:
            Tuple of (workspace, repo_slug, pr_id)
        """
        # Example: https://bitbucket.org/technofarm/geoscan-angular-8/pull-requests/2533
        parsed = urlparse(pr_url)
        path_parts = parsed.path.strip('/').split('/')
        
        if len(path_parts) >= 4 and 'pull-requests' in path_parts:
            workspace = path_parts[0]
            repo_slug = path_parts[1]
            pr_id = int(path_parts[3])
            return workspace, repo_slug, pr_id
        else:
            raise ValueError(f"Invalid Bitbucket PR URL format: {pr_url}")
    
    def get_pull_request(self, workspace: str, repo_slug: str, pr_id: int) -> Dict:
        """
        Fetch pull request details from Bitbucket API
        
        Args:
            workspace: Bitbucket workspace name
            repo_slug: Repository slug
            pr_id: Pull request ID
            
        Returns:
            Pull request data as dictionary
        """
        url = f"{self.base_url}/repositories/{workspace}/{repo_slug}/pullrequests/{pr_id}"
        
        try:
            response = self.session.get(url)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error fetching pull request: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Response status: {e.response.status_code}")
                print(f"Response body: {e.response.text}")
            raise
    
    def get_pull_request_diff(self, workspace: str, repo_slug: str, pr_id: int) -> str:
        """
        Fetch the diff for a pull request
        
        Args:
            workspace: Bitbucket workspace name
            repo_slug: Repository slug
            pr_id: Pull request ID
            
        Returns:
            Diff content as string
        """
        # Get PR details first to get source and destination commits
        pr_data = self.get_pull_request(workspace, repo_slug, pr_id)
        
        source_commit = pr_data['source']['commit']['hash']
        destination_commit = pr_data['destination']['commit']['hash']
        
        # Construct diff spec: source..destination
        diff_spec = f"{destination_commit}..{source_commit}"
        
        url = f"{self.base_url}/repositories/{workspace}/{repo_slug}/diff/{diff_spec}"
        
        try:
            # For diff, we need to set responseType to text
            headers = self.session.headers.copy()
            headers['Accept'] = 'text/plain'
            
            response = self.session.get(url, headers=headers)
            response.raise_for_status()
            return response.text
        except requests.exceptions.RequestException as e:
            print(f"Error fetching diff: {e}")
            raise
    
    def analyze_code_changes(self, diff_content: str, pr_data: Dict) -> ReviewResult:
        """
        Analyze code changes and generate review feedback
        
        Args:
            diff_content: The diff content
            pr_data: Pull request metadata
            
        Returns:
            ReviewResult with analysis and comments
        """
        comments = []
        issues_found = []
        
        # Parse diff to extract file changes
        files_changed = self._parse_diff_files(diff_content)
        
        for file_info in files_changed:
            file_path = file_info['path']
            changes = file_info['changes']
            
            # Analyze each file for potential issues
            file_comments = self._analyze_file_changes(file_path, changes)
            comments.extend(file_comments)
            
            # Check for common code quality issues
            quality_issues = self._check_code_quality(file_path, changes)
            issues_found.extend(quality_issues)
        
        # Generate overall assessment
        score = self._calculate_review_score(issues_found, len(files_changed))
        status = self._determine_review_status(score, issues_found)
        summary = self._generate_review_summary(pr_data, files_changed, issues_found, score)
        
        return ReviewResult(
            status=status,
            summary=summary,
            comments=comments,
            score=score
        )
    
    def _parse_diff_files(self, diff_content: str) -> List[Dict]:
        """Parse diff content to extract file changes"""
        files = []
        current_file = None
        
        for line in diff_content.split('\n'):
            if line.startswith('diff --git'):
                if current_file:
                    files.append(current_file)
                
                # Extract file path
                match = re.search(r'diff --git a/(.*?) b/(.*?)$', line)
                if match:
                    current_file = {
                        'path': match.group(2),
                        'changes': []
                    }
            elif current_file and (line.startswith('+') or line.startswith('-')):
                current_file['changes'].append(line)
        
        if current_file:
            files.append(current_file)
        
        return files
    
    def _analyze_file_changes(self, file_path: str, changes: List[str]) -> List[ReviewComment]:
        """Analyze changes in a specific file"""
        comments = []
        
        # Check for specific patterns based on file type
        if file_path.endswith('.ts') or file_path.endswith('.js'):
            comments.extend(self._analyze_typescript_javascript(file_path, changes))
        elif file_path.endswith('.py'):
            comments.extend(self._analyze_python(file_path, changes))
        elif file_path.endswith('.java'):
            comments.extend(self._analyze_java(file_path, changes))
        
        return comments
    
    def _analyze_typescript_javascript(self, file_path: str, changes: List[str]) -> List[ReviewComment]:
        """Analyze TypeScript/JavaScript specific changes"""
        comments = []
        
        for i, line in enumerate(changes):
            if line.startswith('+'):
                content = line[1:].strip()
                
                # Check for console.log statements
                if 'console.log' in content:
                    comments.append(ReviewComment(
                        content="Consider removing console.log statements before merging to production.",
                        file_path=file_path,
                        line_from=i+1,
                        is_inline=True
                    ))
                
                # Check for TODO comments
                if 'TODO' in content.upper():
                    comments.append(ReviewComment(
                        content="TODO comment found. Consider creating a ticket to track this work.",
                        file_path=file_path,
                        line_from=i+1,
                        is_inline=True
                    ))
                
                # Check for hardcoded URLs
                if re.search(r'https?://[^\s]+', content):
                    comments.append(ReviewComment(
                        content="Hardcoded URL detected. Consider using configuration or environment variables.",
                        file_path=file_path,
                        line_from=i+1,
                        is_inline=True
                    ))
        
        return comments

    def _analyze_python(self, file_path: str, changes: List[str]) -> List[ReviewComment]:
        """Analyze Python specific changes"""
        comments = []

        for i, line in enumerate(changes):
            if line.startswith('+'):
                content = line[1:].strip()

                # Check for print statements
                if content.startswith('print('):
                    comments.append(ReviewComment(
                        content="Consider using logging instead of print statements.",
                        file_path=file_path,
                        line_from=i+1,
                        is_inline=True
                    ))

                # Check for bare except clauses
                if content.strip() == 'except:':
                    comments.append(ReviewComment(
                        content="Avoid bare except clauses. Specify the exception type.",
                        file_path=file_path,
                        line_from=i+1,
                        is_inline=True
                    ))

        return comments

    def _analyze_java(self, file_path: str, changes: List[str]) -> List[ReviewComment]:
        """Analyze Java specific changes"""
        comments = []

        for i, line in enumerate(changes):
            if line.startswith('+'):
                content = line[1:].strip()

                # Check for System.out.println
                if 'System.out.println' in content:
                    comments.append(ReviewComment(
                        content="Consider using a proper logging framework instead of System.out.println.",
                        file_path=file_path,
                        line_from=i+1,
                        is_inline=True
                    ))

        return comments

    def _check_code_quality(self, file_path: str, changes: List[str]) -> List[str]:
        """Check for general code quality issues"""
        issues = []

        added_lines = [line for line in changes if line.startswith('+')]

        # Check for large additions
        if len(added_lines) > 100:
            issues.append(f"Large file change in {file_path} ({len(added_lines)} lines added)")

        # Check for potential security issues
        for line in added_lines:
            content = line[1:].strip().lower()
            if any(keyword in content for keyword in ['password', 'secret', 'token', 'api_key']):
                if '=' in content and not content.startswith('#'):
                    issues.append(f"Potential hardcoded credential in {file_path}")

        return issues

    def _calculate_review_score(self, issues: List[str], files_changed: int) -> int:
        """Calculate a review score from 1-10 based on issues found"""
        base_score = 8

        # Deduct points for issues
        critical_issues = len([issue for issue in issues if 'credential' in issue.lower()])
        major_issues = len([issue for issue in issues if 'large file' in issue.lower()])
        minor_issues = len(issues) - critical_issues - major_issues

        score = base_score - (critical_issues * 3) - (major_issues * 2) - (minor_issues * 0.5)

        # Bonus for small, focused changes
        if files_changed <= 3:
            score += 1

        return max(1, min(10, int(score)))

    def _determine_review_status(self, score: int, issues: List[str]) -> str:
        """Determine review status based on score and issues"""
        critical_issues = any('credential' in issue.lower() for issue in issues)

        if critical_issues:
            return 'rejected'
        elif score >= 7:
            return 'approved'
        elif score >= 5:
            return 'needs_work'
        else:
            return 'rejected'

    def _generate_review_summary(self, pr_data: Dict, files_changed: List[Dict],
                                issues: List[str], score: int) -> str:
        """Generate a comprehensive review summary"""
        title = pr_data.get('title', 'Pull Request')
        author = pr_data.get('author', {}).get('display_name', 'Unknown')

        summary = f"## Review Summary for: {title}\n\n"
        summary += f"**Author:** {author}\n"
        summary += f"**Files Changed:** {len(files_changed)}\n"
        summary += f"**Review Score:** {score}/10\n\n"

        if issues:
            summary += "### Issues Identified:\n"
            for issue in issues:
                summary += f"- {issue}\n"
            summary += "\n"

        # Add recommendations based on score
        if score >= 8:
            summary += "### ✅ Recommendation: APPROVE\n"
            summary += "This pull request demonstrates good code quality with minimal issues. "
            summary += "The changes are well-structured and follow best practices.\n"
        elif score >= 6:
            summary += "### ⚠️ Recommendation: NEEDS WORK\n"
            summary += "This pull request has some issues that should be addressed before merging. "
            summary += "Please review the comments and make the necessary improvements.\n"
        else:
            summary += "### ❌ Recommendation: REJECT\n"
            summary += "This pull request has significant issues that need to be resolved. "
            summary += "Please address all critical issues before resubmitting.\n"

        return summary

    def create_comment(self, workspace: str, repo_slug: str, pr_id: int,
                      comment: ReviewComment) -> Dict:
        """
        Create a comment on the pull request

        Args:
            workspace: Bitbucket workspace name
            repo_slug: Repository slug
            pr_id: Pull request ID
            comment: ReviewComment object

        Returns:
            Created comment data
        """
        url = f"{self.base_url}/repositories/{workspace}/{repo_slug}/pullrequests/{pr_id}/comments"

        comment_data = {
            "content": {
                "raw": comment.content,
                "markup": "markdown"
            }
        }

        # Add inline comment data if specified
        if comment.is_inline and comment.file_path:
            comment_data["inline"] = {
                "path": comment.file_path
            }
            if comment.line_from:
                comment_data["inline"]["from"] = comment.line_from
            if comment.line_to:
                comment_data["inline"]["to"] = comment.line_to

        try:
            response = self.session.post(url, json=comment_data)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error creating comment: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Response: {e.response.text}")
            raise

    def approve_pull_request(self, workspace: str, repo_slug: str, pr_id: int) -> Dict:
        """
        Approve a pull request

        Args:
            workspace: Bitbucket workspace name
            repo_slug: Repository slug
            pr_id: Pull request ID

        Returns:
            Approval response data
        """
        url = f"{self.base_url}/repositories/{workspace}/{repo_slug}/pullrequests/{pr_id}/approve"

        try:
            response = self.session.post(url)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error approving pull request: {e}")
            raise

    def submit_review(self, pr_url: str) -> Dict:
        """
        Complete end-to-end review of a pull request

        Args:
            pr_url: Full Bitbucket PR URL

        Returns:
            Review summary with all actions taken
        """
        try:
            # Parse PR URL
            workspace, repo_slug, pr_id = self.parse_pr_url(pr_url)
            print(f"Reviewing PR {pr_id} in {workspace}/{repo_slug}")

            # Fetch PR details
            print("Fetching pull request details...")
            pr_data = self.get_pull_request(workspace, repo_slug, pr_id)

            # Fetch and analyze diff
            print("Analyzing code changes...")
            diff_content = self.get_pull_request_diff(workspace, repo_slug, pr_id)
            review_result = self.analyze_code_changes(diff_content, pr_data)

            # Create summary comment
            print("Creating review summary...")
            summary_comment = ReviewComment(content=review_result.summary)
            self.create_comment(workspace, repo_slug, pr_id, summary_comment)

            # Create individual comments for issues
            print(f"Creating {len(review_result.comments)} detailed comments...")
            for comment in review_result.comments:
                self.create_comment(workspace, repo_slug, pr_id, comment)

            # Submit approval/rejection
            if review_result.status == 'approved':
                print("Approving pull request...")
                self.approve_pull_request(workspace, repo_slug, pr_id)

            return {
                'status': review_result.status,
                'score': review_result.score,
                'comments_created': len(review_result.comments) + 1,
                'summary': review_result.summary
            }

        except Exception as e:
            print(f"Error during review: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }


def main():
    """Main function to demonstrate the PR reviewer"""

    # Configuration
    API_KEY = "ATATT3xFfGF0TN056ovkNC5hzIwdB8q7PMGK5Zny39O-8qY0p5MMuH7TpqIkaZN9eGPWahGgMKmpdIbGarks30dBeiQS7Ymfqk515jPV4XWaYBfz5oGbz2lbM-r4na8LKjeNFCn_9MCzRBNKd8lKbt7peYGtR-l07kreV5O2wqcytMnfM-SgwT4=2902B8ED"
    PR_URL = "https://bitbucket.org/technofarm/geoscan-angular-8/pull-requests/2533"

    # Initialize reviewer
    reviewer = BitbucketPRReviewer(API_KEY)

    # Submit review
    print("Starting pull request review...")
    result = reviewer.submit_review(PR_URL)

    print("\n" + "="*50)
    print("REVIEW COMPLETED")
    print("="*50)
    print(f"Status: {result['status']}")
    if 'score' in result:
        print(f"Score: {result['score']}/10")
        print(f"Comments Created: {result['comments_created']}")
    if 'error' in result:
        print(f"Error: {result['error']}")
    print("="*50)


if __name__ == "__main__":
    main()

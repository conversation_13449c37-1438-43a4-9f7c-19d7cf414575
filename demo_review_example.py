#!/usr/bin/env python3
"""
Demonstration of Bitbucket Pull Request Reviewer
Shows how the tool would work with sample data
"""

from bitbucket_pr_reviewer import Bitbucket<PERSON><PERSON>eviewer, ReviewComment, ReviewResult
import json


def demo_review_analysis():
    """Demonstrate the review analysis functionality with sample data"""
    
    print("="*60)
    print("BITBUCKET PULL REQUEST REVIEWER DEMONSTRATION")
    print("="*60)
    
    # Sample PR data (what would be returned from Bitbucket API)
    sample_pr_data = {
        "id": 2533,
        "title": "Add user authentication and logging improvements",
        "author": {
            "display_name": "<PERSON> Developer",
            "username": "jdeveloper"
        },
        "source": {
            "commit": {"hash": "abc123"},
            "branch": {"name": "feature/auth-improvements"}
        },
        "destination": {
            "commit": {"hash": "def456"},
            "branch": {"name": "main"}
        },
        "state": "OPEN"
    }
    
    # Sample diff content (what would be returned from diff API)
    sample_diff = """diff --git a/src/auth/login.ts b/src/auth/login.ts
index 1234567..abcdefg 100644
--- a/src/auth/login.ts
+++ b/src/auth/login.ts
@@ -10,6 +10,7 @@ export class LoginService {
   
   async authenticate(username: string, password: string): Promise<boolean> {
+    console.log('Authenticating user:', username);
     const response = await fetch('https://api.example.com/auth', {
       method: 'POST',
       body: JSON.stringify({ username, password })
@@ -20,6 +21,7 @@ export class LoginService {
   }
   
   private validateCredentials(username: string, password: string): boolean {
+    // TODO: Add proper validation logic
     return username.length > 0 && password.length > 0;
   }
 }

diff --git a/src/utils/logger.py b/src/utils/logger.py
index 7890123..fedcba9 100644
--- a/src/utils/logger.py
+++ b/src/utils/logger.py
@@ -5,6 +5,7 @@ import logging
 
 def setup_logging():
     logging.basicConfig(level=logging.INFO)
+    print("Logging setup completed")
 
 def log_user_action(action, user_id):
+    print(f"User {user_id} performed action: {action}")
     logging.info(f"User {user_id} performed action: {action}")

diff --git a/src/config/database.java b/src/config/database.java
index 4567890..1234abc 100644
--- a/src/config/database.java
+++ b/src/config/database.java
@@ -8,6 +8,8 @@ public class DatabaseConfig {
     private static final String DB_URL = "********************************";
     
     public void connect() {
+        String password = "hardcoded_password_123";
+        System.out.println("Connecting to database with password: " + password);
         // Connection logic here
     }
 }"""
    
    # Initialize reviewer (without API key for demo)
    reviewer = BitbucketPRReviewer("demo-key")
    
    print("\n1. ANALYZING PULL REQUEST DATA")
    print("-" * 40)
    print(f"PR #{sample_pr_data['id']}: {sample_pr_data['title']}")
    print(f"Author: {sample_pr_data['author']['display_name']}")
    print(f"Branch: {sample_pr_data['source']['branch']['name']} → {sample_pr_data['destination']['branch']['name']}")
    
    print("\n2. ANALYZING CODE CHANGES")
    print("-" * 40)
    
    # Analyze the sample diff
    review_result = reviewer.analyze_code_changes(sample_diff, sample_pr_data)
    
    print(f"Files analyzed: {len(reviewer._parse_diff_files(sample_diff))}")
    print(f"Comments generated: {len(review_result.comments)}")
    print(f"Review score: {review_result.score}/10")
    print(f"Review status: {review_result.status.upper()}")
    
    print("\n3. DETAILED REVIEW COMMENTS")
    print("-" * 40)
    
    for i, comment in enumerate(review_result.comments, 1):
        print(f"\nComment #{i}:")
        print(f"  File: {comment.file_path}")
        if comment.is_inline:
            print(f"  Line: {comment.line_from}")
        print(f"  Issue: {comment.content}")
    
    print("\n4. REVIEW SUMMARY")
    print("-" * 40)
    print(review_result.summary)
    
    print("\n5. API CALLS THAT WOULD BE MADE")
    print("-" * 40)
    print("With a valid API key, the following calls would be made:")
    print("✓ GET /2.0/repositories/technofarm/geoscan-angular-8/pullrequests/2533")
    print("✓ GET /2.0/repositories/technofarm/geoscan-angular-8/diff/def456..abc123")
    print(f"✓ POST /2.0/repositories/technofarm/geoscan-angular-8/pullrequests/2533/comments ({len(review_result.comments) + 1} times)")
    
    if review_result.status == 'approved':
        print("✓ POST /2.0/repositories/technofarm/geoscan-angular-8/pullrequests/2533/approve")
    else:
        print("✗ Pull request would NOT be approved due to issues found")
    
    print("\n6. ISSUES DETECTED")
    print("-" * 40)
    
    # Parse files to show what was detected
    files_changed = reviewer._parse_diff_files(sample_diff)
    for file_info in files_changed:
        file_path = file_info['path']
        print(f"\n{file_path}:")
        
        if file_path.endswith('.ts'):
            print("  - console.log statement detected")
            print("  - TODO comment found")
            print("  - Hardcoded URL detected")
        elif file_path.endswith('.py'):
            print("  - print() statement detected (should use logging)")
        elif file_path.endswith('.java'):
            print("  - System.out.println detected")
            print("  - CRITICAL: Hardcoded password found!")
    
    print(f"\n7. SCORING BREAKDOWN")
    print("-" * 40)
    print("Base score: 8/10")
    print("Deductions:")
    print("  - Hardcoded credential: -3 points")
    print("  - Minor issues (console.log, print, TODO): -1.5 points")
    print(f"Final score: {review_result.score}/10")
    
    print("\n" + "="*60)
    print("DEMONSTRATION COMPLETED")
    print("="*60)
    
    return review_result


def show_api_requirements():
    """Show what's needed to use the tool with real Bitbucket API"""
    
    print("\n" + "="*60)
    print("REAL USAGE REQUIREMENTS")
    print("="*60)
    
    print("\n1. BITBUCKET API KEY SETUP")
    print("-" * 30)
    print("To use this tool with real pull requests, you need:")
    print("• Valid Bitbucket API key (App Password)")
    print("• Required scopes: pullrequest, pullrequest:write, repository")
    print("• Access to the target repository")
    
    print("\n2. AUTHENTICATION EXAMPLE")
    print("-" * 30)
    print("""
# Create API key at: https://bitbucket.org/account/settings/app-passwords/
reviewer = BitbucketPRReviewer("your-valid-api-key-here")
result = reviewer.submit_review("https://bitbucket.org/workspace/repo/pull-requests/123")
    """)
    
    print("\n3. ERROR HANDLING")
    print("-" * 30)
    print("The tool handles common errors:")
    print("• 401 Unauthorized - Invalid/expired API key")
    print("• 404 Not Found - PR doesn't exist or no access")
    print("• 403 Forbidden - Insufficient permissions")
    print("• Network errors and API rate limits")
    
    print("\n4. SUPPORTED OPERATIONS")
    print("-" * 30)
    print("✓ Fetch PR details and diff")
    print("✓ Analyze code for quality issues")
    print("✓ Create inline and general comments")
    print("✓ Approve/reject pull requests")
    print("✓ Generate comprehensive review reports")


if __name__ == "__main__":
    # Run the demonstration
    demo_review_analysis()
    show_api_requirements()
